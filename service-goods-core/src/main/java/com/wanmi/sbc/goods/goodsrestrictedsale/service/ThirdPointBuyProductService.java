package com.wanmi.sbc.goods.goodsrestrictedsale.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.wanmi.sbc.common.base.BaseResponse;
import com.wanmi.sbc.common.enums.ThirdSaleRuleFlag;
import com.wanmi.sbc.common.exception.SbcRuntimeException;
import com.wanmi.sbc.common.util.CommonErrorCode;
import com.wanmi.sbc.customer.api.provider.store.StoreQueryProvider;
import com.wanmi.sbc.customer.api.response.store.StoreMappingInfoResponse;
import com.wanmi.sbc.goods.api.request.goodsrestrictedsale.GoodsRestrictedBatchValidateRequest;
import com.wanmi.sbc.goods.api.response.pointsgoods.PointCheckResponse;
import com.wanmi.sbc.goods.bean.vo.GoodsRestrictedValidateVO;
import com.wanmi.sbc.goods.info.model.root.GoodsInfo;
import com.wanmi.sbc.goods.info.repository.GoodsInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.DefaultClientConnectionReuseStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ThirdPointBuyProductService {
    @Resource
    private GoodsInfoRepository goodsInfoRepository;
    @Resource
    private StoreQueryProvider storeQueryProvider;
    private final String pointBuyProductCheck = "/pointBuyProductCheck";
    private final String DEFAULT_SUCCESS_CODE = "0000";

    private static RestTemplate restTemplate = new RestTemplate();

    static {
        List<HttpMessageConverter<?>> converters = restTemplate.getMessageConverters();
        for (int i = 0, size = converters.size(); i < size; i++) {
            if (converters.get(i) instanceof StringHttpMessageConverter) {
                converters.remove(i);
                converters.add(i, new StringHttpMessageConverter(Charset.forName(StandardCharsets.UTF_8.name())));
                break;
            }
        }

        // 连接池
        HttpClient httpClient = HttpClientBuilder
                .create()
                .setMaxConnTotal(512)
                .setMaxConnPerRoute(512)
                .evictIdleConnections(1200, TimeUnit.SECONDS)
                .setConnectionReuseStrategy(DefaultClientConnectionReuseStrategy.INSTANCE)
                .build();

        // 连接超时
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        factory.setConnectionRequestTimeout(3000);
        factory.setConnectTimeout(3000);
        factory.setReadTimeout(15000);

        restTemplate.setRequestFactory(factory);
    }


    public void pointBuyProductCheck(GoodsRestrictedBatchValidateRequest validateRequest, StoreMappingInfoResponse storeMappingInfo){

        List<GoodsInfo> byGoodsInfoIds = goodsInfoRepository.findByGoodsInfoIds(validateRequest.getGoodsRestrictedValidateVOS().stream().map(GoodsRestrictedValidateVO::getSkuId).collect(Collectors.toList()));

        BaseResponse baseResponse = sendThirdPlatformPointButProductCheck(validateRequest.getCustomerVO().getCustomerId(), validateRequest.getCustomerVO().getStoreId(), byGoodsInfoIds, storeMappingInfo);

        if(!Objects.equals(baseResponse.getCode(), CommonErrorCode.SUCCESSFUL)){
            throw new SbcRuntimeException(CommonErrorCode.FAILED, baseResponse.getMessage());
        }
    }

    /**
     * 测试方法 页面点击后搜寻店铺内的积分商品 然后向目标地址发起请求
     * 如果没有积分商品则进行提示 然后在进行请求
     * @param goodsInfoId
     * @param stroeId
     * @param customerId
     */
    public void testPushPointBuyProductCheck(String goodsInfoId, Long stroeId, String customerId){
        if(Objects.isNull(goodsInfoId)){
            throw new SbcRuntimeException("没有找到积分商品, 请先创建商品后重试");
        }

        StoreMappingInfoResponse storeMappingInfo = storeQueryProvider.findStoreMappingInfo(stroeId).getContext();
        if(Objects.isNull(storeMappingInfo) || StringUtils.isBlank(storeMappingInfo.getWebHook())){
            throw new SbcRuntimeException("该店铺没有配置第三方积分限售校验");
        }

        List<GoodsInfo> byGoodsInfoIds = goodsInfoRepository.findByGoodsInfoIds(Arrays.asList(goodsInfoId));

        BaseResponse baseResponse = sendThirdPlatformPointButProductCheck(customerId, stroeId, byGoodsInfoIds, storeMappingInfo);

        if(!Objects.equals(baseResponse.getCode(), CommonErrorCode.SUCCESSFUL)){
            throw new SbcRuntimeException(CommonErrorCode.FAILED, baseResponse.getMessage());
        }
    }

    public BaseResponse<PointCheckResponse> pushPointBuyProductCheck(String goodsInfoId, Long storeId, String customerId){
        if(Strings.isNullOrEmpty(goodsInfoId)){
            throw new SbcRuntimeException("没有找到积分商品, 请先创建商品后重试");
        }
        //查看店铺关联三方配置表，如果有售卖规则校验且有webhook的情况才需要进行校验
        StoreMappingInfoResponse storeMappingInfo = storeQueryProvider.findStoreMappingInfo(storeId).getContext();
        if(Objects.isNull(storeMappingInfo) ||
            Strings.isNullOrEmpty(storeMappingInfo.getWebHook()) ||
            ThirdSaleRuleFlag.NO_NEED.toValue() == storeMappingInfo.getThirdSaleRuleFlag() ){
            PointCheckResponse pointCheckResponse = new PointCheckResponse();
            pointCheckResponse.setCode(DEFAULT_SUCCESS_CODE);
            return BaseResponse.success(pointCheckResponse);
        }
        List<GoodsInfo> byGoodsInfoIds = goodsInfoRepository.findByGoodsInfoIds(Collections.singletonList(goodsInfoId));
        return sendThirdPlatformPointButProductCheck(customerId, storeId, byGoodsInfoIds, storeMappingInfo);
    }


    public BaseResponse<PointCheckResponse> sendThirdPlatformPointButProductCheck(String customerId, Long storeId, List<GoodsInfo> goodsInfos, StoreMappingInfoResponse storeMappingInfo){
        HashMap<String, Object> signMaps = Maps.newHashMap();
        signMaps.put("customerId", customerId);
        signMaps.put("storeId", storeId);
        signMaps.put("goodsInfos", goodsInfos);
        signMaps.put("sign", getSign(signMaps));

        PointCheckResponse pointCheckResponse = new PointCheckResponse();
        try{
            log.info("uri point check request: {},  param:{}", storeMappingInfo.getWebHook()+ pointBuyProductCheck, signMaps);
            String jsonResponseStr = restTemplate.postForObject(storeMappingInfo.getWebHook() + pointBuyProductCheck, signMaps, String.class);
            pointCheckResponse = JSONObject.parseObject(jsonResponseStr, PointCheckResponse.class);
        }catch (Exception e){
            log.error("第三方平台积分校验异常", e);
            return BaseResponse.error("请求异常: " + e.getMessage());

        }
        log.info("uri point check response store:{}, customerId:{}, skuIds:{}, return:{}", storeId, customerId, goodsInfos.stream().map(GoodsInfo::getGoodsInfoId).collect(Collectors.toList()), pointCheckResponse);
        return BaseResponse.success(pointCheckResponse);
    }


    private static String getSign(Map<String, Object> map) {
        TreeMap<String, Object> treeMap = new TreeMap<>(map);
        if (CollectionUtils.isEmpty(map)) {
            return null;
        }
        String string = "";
        for (String key : treeMap.keySet()) {
            Object o = treeMap.get(key);
            if (o == null) {
                continue;
            }
            String value = String.valueOf(o);
            if (Strings.isNullOrEmpty(value) || key.equalsIgnoreCase("sign")) {
                continue;
            }
            string = string + key + "=" + value + "&";
        }
        if (!StringUtils.isEmpty(string)) {
            string = string.substring(0, string.length() - 1);
        }
        return digest(string);
    }
    private static String digest(String text) {
        String result = "";
        try {
            MessageDigest md = MessageDigest.getInstance("md5");
            // 定义编码方式
            byte[] bufs = text.getBytes("UTF-8");
            md.update(bufs);
            byte[] b = md.digest();
            int i;
            StringBuffer buf = new StringBuffer("");
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0) {
                    i += 256;
                }
                if (i < 16) {
                    buf.append("0");
                }
                buf.append(Integer.toHexString(i));
            }
            result = buf.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

}
