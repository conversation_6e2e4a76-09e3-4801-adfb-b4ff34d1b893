package com.wanmi.sbc.goods.goodsexceptionlog;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wanmi.sbc.common.util.CustomLocalDateTimeDeserializer;
import com.wanmi.sbc.common.util.CustomLocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName GoodsExceptionLogRequest
 * @Description GoodsExceptionLogRequest
 * <AUTHOR>
 * @Date 2022/1/19 17:51
 * @Version 1.0
 */
@Data
@Entity
@Table(name = "goods_exception_log")
@NoArgsConstructor
@AllArgsConstructor
public class GoodsExceptionLog implements Serializable {

    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ex_id")
    private Long exId;

    /**
     * 异常类型(price:价格异常; status:状态异常; stock:库存异常;)
     */
    @Column(name = "ex_type")
    private String exType;

    /**
     * 触发场景(change:商品变更; added:重新上架; cancel:手动取消;)
     */
    @Column(name = "touch_scene")
    private String touchScene;

    /**
     * 供应商商品SPU编码
     */
    @Column(name = "provider_goods_no")
    private String providerGoodsNo;

    /**
     * 供应商商品id
     */
    @Column(name = "provider_goods_id")
    private String providerGoodsId;

    /**
     * 福利主商品SPU编码
     */
    @Column(name = "supplier_goods_no")
    private String supplierGoodsNo;

    /**
     * 福利主商品id
     */
    @Column(name = "supplier_goods_id")
    private String supplierGoodsId;

    /**
     * 创建时间
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "create_time")
    private LocalDateTime createTime;

}
