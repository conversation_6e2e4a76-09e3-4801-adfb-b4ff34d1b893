package com.wanmi.sbc.goods.goodsinfocombinationgoods.service;

import com.wanmi.sbc.common.enums.DeleteFlag;
import com.wanmi.sbc.goods.api.response.goodsinfocombinationgoods.CombinationGoodsDetailByCombinationGoodsIdResponse;
import com.wanmi.sbc.goods.bean.dto.GoodsInfoMinusStockDTO;
import com.wanmi.sbc.goods.bean.dto.GoodsInfoPlusStockDTO;
import com.wanmi.sbc.goods.bean.dto.GoodsMinusStockDTO;
import com.wanmi.sbc.goods.bean.dto.GoodsPlusStockDTO;
import com.wanmi.sbc.goods.bean.vo.GoodsInfoVO;
import com.wanmi.sbc.goods.bean.vo.GoodsVO;
import com.wanmi.sbc.goods.cate.model.root.GoodsCate;
import com.wanmi.sbc.goods.cate.service.GoodsCateService;
import com.wanmi.sbc.goods.info.model.root.Goods;
import com.wanmi.sbc.goods.info.model.root.GoodsInfo;
import com.wanmi.sbc.goods.info.model.root.GoodsInfoCombinationGoods;
import com.wanmi.sbc.goods.info.repository.GoodsInfoCombinationGoodsRepository;
import com.wanmi.sbc.goods.info.request.GoodsInfoQueryRequest;
import com.wanmi.sbc.goods.info.request.GoodsQueryRequest;
import com.wanmi.sbc.goods.info.service.GoodsInfoService;
import com.wanmi.sbc.goods.info.service.GoodsService;
import com.wanmi.sbc.goods.info.service.GoodsStockService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.wanmi.sbc.goods.api.request.goodsinfocombinationgoods.GoodsInfoCombinationGoodsQueryRequest;
import com.wanmi.sbc.goods.bean.vo.GoodsInfoCombinationGoodsVO;
import com.wanmi.sbc.common.util.KsBeanUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>组合商品业务逻辑</p>
 * <AUTHOR>
 * @date 2021-10-13 14:50:29
 */
@Service("GoodsInfoCombinationGoodsService")
public class GoodsInfoCombinationGoodsService {
	@Autowired
	private GoodsInfoCombinationGoodsRepository goodsInfoCombinationGoodsRepository;

	@Autowired
	private GoodsInfoService goodsInfoService;

	@Autowired
	private GoodsService goodsService;

	@Autowired
	private GoodsCateService goodsCateService;

	@Autowired
	private GoodsStockService goodsStockService;
	
	/** 
	 * 新增组合商品
	 * <AUTHOR>
	 */
	@Transactional
	public GoodsInfoCombinationGoods add(GoodsInfoCombinationGoods entity) {
		goodsInfoCombinationGoodsRepository.save(entity);
		return entity;
	}
	
	/** 
	 * 修改组合商品
	 * <AUTHOR>
	 */
	@Transactional
	public GoodsInfoCombinationGoods modify(GoodsInfoCombinationGoods entity) {
		goodsInfoCombinationGoodsRepository.save(entity);
		return entity;
	}

	/**
	 * 单个删除组合商品
	 * <AUTHOR>
	 */
	@Transactional
	public void deleteById(Long id) {
		goodsInfoCombinationGoodsRepository.deleteById(id);
	}
	
	/** 
	 * 批量删除组合商品
	 * <AUTHOR>
	 */
	@Transactional
	public void deleteByIdList(List<Long> ids) {
		goodsInfoCombinationGoodsRepository.deleteByIdList(ids);
	}
	
	/** 
	 * 单个查询组合商品
	 * <AUTHOR>
	 */
	public GoodsInfoCombinationGoods getById(Long id){
		return goodsInfoCombinationGoodsRepository.findById(id).orElse(null);
	}
	
	/** 
	 * 分页查询组合商品
	 * <AUTHOR>
	 */
	public Page<GoodsInfoCombinationGoods> page(GoodsInfoCombinationGoodsQueryRequest queryReq){
		return goodsInfoCombinationGoodsRepository.findAll(
				GoodsInfoCombinationGoodsWhereCriteriaBuilder.build(queryReq),
				queryReq.getPageRequest());
	}
	
	/** 
	 * 列表查询组合商品
	 * <AUTHOR>
	 */
	public List<GoodsInfoCombinationGoods> list(GoodsInfoCombinationGoodsQueryRequest queryReq){
		return goodsInfoCombinationGoodsRepository.findAll(
				GoodsInfoCombinationGoodsWhereCriteriaBuilder.build(queryReq),
				queryReq.getSort());
	}

	/**
	 * 将实体包装成VO
	 * <AUTHOR>
	 */
	public GoodsInfoCombinationGoodsVO wrapperVo(GoodsInfoCombinationGoods goodsInfoCombinationGoods) {
		if (goodsInfoCombinationGoods != null){
			GoodsInfoCombinationGoodsVO goodsInfoCombinationGoodsVO=new GoodsInfoCombinationGoodsVO();
			KsBeanUtil.copyPropertiesThird(goodsInfoCombinationGoods,goodsInfoCombinationGoodsVO);
			return goodsInfoCombinationGoodsVO;
		}
		return null;
	}

	/**
	 * 查询组合商品包含关系及包含sku的详情
	 * @param combinationGoodsId
	 * @return
	 */
    public CombinationGoodsDetailByCombinationGoodsIdResponse getCombinationGoodsDetailByCombinationGoodsId(String combinationGoodsId) {
		GoodsInfoCombinationGoodsQueryRequest queryReq = GoodsInfoCombinationGoodsQueryRequest.builder()
				.combinationGoodsId(combinationGoodsId).delFlag(DeleteFlag.NO).build();
		List<GoodsInfoCombinationGoods> goodsInfoCombinationGoodsList = list(queryReq);
		List<String> goodsInfoIds = goodsInfoCombinationGoodsList.stream().map(GoodsInfoCombinationGoods::getGoodsInfoId)
				.collect(Collectors.toList());
		GoodsInfoQueryRequest goodsInfoQueryRequest = GoodsInfoQueryRequest.builder().goodsInfoIds(goodsInfoIds)
				.delFlag(DeleteFlag.NO.toValue()).build();
		List<GoodsInfo> goodsInfoList = goodsInfoService.findByParams(goodsInfoQueryRequest);
		List<String> goodsIds = goodsInfoList.stream().map(GoodsInfo::getGoodsId).collect(Collectors.toList());
		GoodsQueryRequest goodsQueryRequest = GoodsQueryRequest.builder().goodsIds(goodsIds).delFlag(DeleteFlag.NO.toValue()).build();
		List<Goods> goodsList = goodsService.findAll(goodsQueryRequest);
		List<Long> cateIds = goodsInfoList.stream().map(GoodsInfo::getCateId).collect(Collectors.toList());
		List<GoodsCate> goodsCateList = goodsCateService.findByIds(cateIds);
		Map<Long, String> goodsCateMap = goodsCateList.stream().collect(Collectors.toMap(GoodsCate::getCateId, GoodsCate::getCateName));
		List<GoodsVO> goodsVOList = KsBeanUtil.convertList(goodsList, GoodsVO.class);
		Map<String, GoodsVO> goodsVOMap = goodsVOList.stream().collect(Collectors.toMap(GoodsVO::getGoodsId, Function.identity()));
		List<GoodsInfoVO> goodsInfoVOList = KsBeanUtil.convertList(goodsInfoList, GoodsInfoVO.class);
		goodsInfoVOList.forEach(goodsInfoVO->{
			goodsInfoVO.setGoods(goodsVOMap.get(goodsInfoVO.getGoodsId()));
			goodsInfoVO.setCateName(goodsCateMap.get(goodsInfoVO.getCateId()));
		});
		List<GoodsInfoCombinationGoodsVO> goodsInfoCombinationGoodsVOList = KsBeanUtil.convertList(goodsInfoCombinationGoodsList, GoodsInfoCombinationGoodsVO.class);
		Map<String, GoodsInfoVO> goodsInfoVOMap = goodsInfoVOList.stream().collect(Collectors.toMap(GoodsInfoVO::getGoodsInfoId, Function.identity()));
		goodsInfoCombinationGoodsVOList.forEach(goodsInfoCombinationGoodsVO->{
			goodsInfoCombinationGoodsVO.setGoodsInfo(goodsInfoVOMap.get(goodsInfoCombinationGoodsVO.getGoodsInfoId()));
		});
		GoodsInfo combinationGoodsInfo = goodsInfoService.getById(combinationGoodsId);
		GoodsInfoVO combinationGoodsInfoVO = KsBeanUtil.convert(combinationGoodsInfo, GoodsInfoVO.class);
		return CombinationGoodsDetailByCombinationGoodsIdResponse.builder().combinationGoodsInfo(combinationGoodsInfoVO)
				.goodsInfoCombinationGoodsVOList(goodsInfoCombinationGoodsVOList).build();
	}

	/**
	 * 根据id扣减组合库存中sku库存
	 * @param subStock
	 * @param id
	 * @return
	 */
	@Transactional
    public int subStockById(Long subStock, Long id) {
    	return goodsInfoCombinationGoodsRepository.subStockById(subStock, id);
    }

	/**
	 * 根据id增加组合库存中sku库存
	 * @param addStock
	 * @param id
	 * @return
	 */
	@Transactional
	public int addStockById(Long addStock, Long id) {
		return goodsInfoCombinationGoodsRepository.addStockById(addStock, id);
	}

	/**
	 * 批量新增组合商品
	 * @param goodsInfoCombinationGoodsList
	 */
	@Transactional
	public void saveAll(List<GoodsInfoCombinationGoods> goodsInfoCombinationGoodsList) {
		goodsInfoCombinationGoodsRepository.saveAll(goodsInfoCombinationGoodsList);
	}

	/**
	 * 批量释放组合商品库存的定时任务
	 */
	@Transactional
    public void freeExpireCombinationGoodsStock() {
		List<GoodsInfoCombinationGoods> goodsInfoCombinationGoodsList = goodsInfoCombinationGoodsRepository.getExpireCombinationGoods();
		if(CollectionUtils.isNotEmpty(goodsInfoCombinationGoodsList)){
			List<String> combinationGoodsInfoIds = goodsInfoCombinationGoodsList.stream()
					.map(GoodsInfoCombinationGoods::getCombinationGoodsId).distinct().collect(Collectors.toList());
			List<GoodsInfo> combinationGoodsInfoList = goodsInfoService.findByParams(GoodsInfoQueryRequest.builder().goodsInfoIds(combinationGoodsInfoIds)
					.delFlag(DeleteFlag.NO.toValue()).build());

			if(CollectionUtils.isNotEmpty(combinationGoodsInfoList)){
				//减去组合商品库存
				List<GoodsMinusStockDTO> combinationSpuStockList = new ArrayList<>();
				List<GoodsInfoMinusStockDTO> combinationSkuStockList = combinationGoodsInfoList.stream().map(combinationGoodsInfo -> {
					GoodsInfoMinusStockDTO dto = new GoodsInfoMinusStockDTO();
					dto.setStock(combinationGoodsInfo.getStock());
					combinationSpuStockList.add(new GoodsMinusStockDTO(combinationGoodsInfo.getStock(), combinationGoodsInfo.getGoodsId()));
					dto.setGoodsInfoId(combinationGoodsInfo.getGoodsInfoId());
					return dto;
				}).collect(Collectors.toList());
				goodsInfoService.batchSubStock(combinationSkuStockList);
				goodsStockService.batchSubStock(combinationSpuStockList);
				//加上供应商端对应的库存
				List<String> goodsInfoIds = goodsInfoCombinationGoodsList.stream().map(GoodsInfoCombinationGoods::getGoodsInfoId).distinct().collect(Collectors.toList());
				List<GoodsInfo> goodsInfoList = goodsInfoService.findByIds(goodsInfoIds);
				Map<String, GoodsInfo> goodsInfoMap = goodsInfoList.stream().collect(Collectors.toMap(GoodsInfo::getGoodsInfoId, Function.identity()));
				List<GoodsPlusStockDTO> spuStockList = new ArrayList<>();
				List<GoodsInfoPlusStockDTO> stockList = goodsInfoCombinationGoodsList.stream().map(goodsInfoCombinationGoods -> {
					GoodsInfoPlusStockDTO dto = new GoodsInfoPlusStockDTO();
					dto.setStock(goodsInfoCombinationGoods.getStock());
					spuStockList.add(new GoodsPlusStockDTO(goodsInfoCombinationGoods.getStock(), goodsInfoMap.get(goodsInfoCombinationGoods.getGoodsInfoId()).getGoodsId()));
					dto.setGoodsInfoId(goodsInfoCombinationGoods.getGoodsInfoId());
					return dto;
				}).collect(Collectors.toList());
				goodsInfoService.batchAddStock(stockList);
				//spu加库存
				goodsStockService.batchAddStock(spuStockList);
			}

		}
    }

	/**
	 * 根据goodsInfoIds 获取未结束的组合商品活动中的虚拟商品
	 * @param goodsInfoIds
	 * @return
	 */
	public List<GoodsInfoCombinationGoods> getActiveGoodsInfoByGoodsInfoIds(List<String> goodsInfoIds) {
		List<GoodsInfoCombinationGoods> goodsInfoCombinationGoodsList =  goodsInfoCombinationGoodsRepository.getActiveGoodsInfoByGoodsInfoIds(goodsInfoIds);
		return goodsInfoCombinationGoodsList;
	}
}
