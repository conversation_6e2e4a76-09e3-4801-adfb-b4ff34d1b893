package com.wanmi.sbc.goods.restrictedrecord.model.root;

import lombok.Data;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @desc
 * @date 2022-12-01
 */
@Data
@Entity
@Table(name = "restricted_record_order")
public class RestrictedRecordOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记录主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 会员的主键
     */
    @Column(name = "customer_id")
    private String customerId;

    /**
     * 货品主键
     */
    @Column(name = "goods_info_id")
    private String goodsInfoId;
    /**
     * 订单ID
     */
    @Column(name = "trade_id")
    private String tradeId;

    /**
     * 订单类型
     */
    @Column(name = "order_type")
    private String orderType;
    /**
     * 开始时间
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "create_time")
    private LocalDateTime createTime;



}
