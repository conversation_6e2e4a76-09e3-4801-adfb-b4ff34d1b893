package com.wanmi.sbc.goods.restrictedrecord.repository;

import com.wanmi.sbc.goods.restrictedrecord.model.root.RestrictedRecordOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2022-12-01
 */
@Repository
public interface RestrictedRecordOrderRepository extends JpaRepository<RestrictedRecordOrder, Long>,
        JpaSpecificationExecutor<RestrictedRecordOrder> {

    /**
     * 查询逆向操作记录。
     * @param customerId
     * @param goodsInfoId
     * @return
     */
    @Query("from RestrictedRecordOrder r where r.tradeId = ?1 and r.customerId = ?2 and r.goodsInfoId=?3")
    List<RestrictedRecordOrder> findByTradeIdAndCustomerIdAndGoodsInfoId(String tradeId, String customerId, String goodsInfoId);

}
