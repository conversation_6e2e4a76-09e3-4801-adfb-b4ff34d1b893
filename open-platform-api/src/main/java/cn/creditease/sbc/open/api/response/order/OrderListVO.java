package cn.creditease.sbc.open.api.response.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName OrderListVO
 * @Description OrderListVO
 * <AUTHOR>
 * @Date 2021/12/3 18:05
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderListVO {

    /** 订单id */
    private String tid;
    /** 订单状态(NOT_AUDIT:待审核;NOT_PAID:待付款;UNCONFIRMED:待确认;WAIT_DELIVERED:待发货;WAIT_PAY_EARNEST:待支付定金;WAIT_PAY_TAIL:待支付尾款;NOT_DELIVERED:待收货;DELIVERED:已收货;COMPLETED:已完成;VOID:已作废;) */
    private String tradeState;
    /** 店铺id */
    private Long storeId;
    /** 客户编号(用户id) */
    private String customerId;
    /** 订单实付金额 */
    private BigDecimal tradePrice;
    /**
     * 消耗积分
     */
    private Long buyPoint;
    /** 订单对象集合 */
    private List<TradeVO> trades;
    /** 订单创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /** 订单最后修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifiedTime;
    /**
     * 支付状态
     */
    private String payState;
    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    //组合商品信息
    private List<CombinationItemVO> combinationTradeItems = new ArrayList<>();

    //子订单商品列表
    private List<TradeItemVO> tradeItems = new ArrayList<>();

    //发货单
    private List<TradeDeliverVO> tradeDelivers = new ArrayList<>();


    //订单类型
    private String orderType;

    //CDK兑换码
    private String cdkCode;
}
