package com.wanmi.sbc.order.trade.service;

import com.wanmi.sbc.common.base.Operator;
import com.wanmi.sbc.common.enums.BoolFlag;
import com.wanmi.sbc.common.exception.SbcRuntimeException;
import com.wanmi.sbc.common.util.Constants;
import com.wanmi.sbc.common.util.GeneratorService;
import com.wanmi.sbc.common.util.KsBeanUtil;
import com.wanmi.sbc.order.api.request.trade.TradeUpdateRequest;
import com.wanmi.sbc.order.bean.enums.*;
import com.wanmi.sbc.order.common.OperationLogMq;
import com.wanmi.sbc.order.returnorder.model.root.ReturnOrder;
import com.wanmi.sbc.order.returnorder.repository.ReturnOrderRepository;
import com.wanmi.sbc.order.returnorder.service.ReturnOrderService;
import com.wanmi.sbc.order.trade.fsm.event.TradeEvent;
import com.wanmi.sbc.order.trade.model.entity.TradeDeliver;
import com.wanmi.sbc.order.trade.model.entity.TradeItem;
import com.wanmi.sbc.order.trade.model.entity.TradeState;
import com.wanmi.sbc.order.trade.model.entity.value.ShippingItem;
import com.wanmi.sbc.order.trade.model.entity.value.TradeEventLog;
import com.wanmi.sbc.order.trade.model.root.ProviderTrade;
import com.wanmi.sbc.order.trade.model.root.Trade;
import com.wanmi.sbc.order.trade.repository.ProviderTradeRepository;
import com.wanmi.sbc.order.trade.request.ProviderTradeQueryRequest;
import com.wanmi.sbc.order.trade.request.TradeDeliverRequest;
import com.wanmi.sbc.setting.api.provider.AuditQueryProvider;
import com.wanmi.sbc.setting.api.request.SupplierOrderAuditRequest;
import com.wanmi.sbc.setting.api.request.TradeConfigGetByTypeRequest;
import com.wanmi.sbc.setting.bean.enums.ConfigType;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 供应商订单处理服务层
 * @Autho qiaokang
 * @Date：2020-02-11 22:56
 */
@Service
@Slf4j
@RefreshScope
public class ProviderTradeService {

    @Autowired
    private ProviderTradeRepository providerTradeRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private AuditQueryProvider auditQueryProvider;

    @Autowired
    private ReturnOrderRepository returnOrderRepository;

    @Autowired
    private GeneratorService generatorService;

    @Autowired
    private OperationLogMq operationLogMq;

    @Autowired
    private TradeService tradeService;

    @Autowired
    private TradeCacheService tradeCacheService;

    @Autowired
    private ReturnOrderService returnOrderService;
    @Value("${trade.export.num}")
    private String exportTradeNum;


    /**
     * 新增文档
     * 专门用于数据新增服务,不允许数据修改的时候调用
     *
     * @param trade
     */
    public void addProviderTrade(ProviderTrade trade) {
        providerTradeRepository.save(trade);
    }

    /**
     * 修改文档
     * 专门用于数据修改服务,不允许数据新增的时候调用
     *
     * @param trade
     */
    public void updateProviderTrade(ProviderTrade trade) {
        providerTradeRepository.save(trade);
    }

    /**
     * 修改文档
     * 专门用于数据修改服务,不允许数据新增的时候调用
     *
     * @param tradeList
     */
    public void updateProviderTradeList(List<ProviderTrade> tradeList) {
        providerTradeRepository.saveAll(tradeList);
    }

    /**
     * 删除文档
     *
     * @param tid
     */
    public void deleteProviderTrade(String tid) {
        providerTradeRepository.deleteById(tid);
    }

    /**
     * 根据父订单号查询供货商订单
     *
     * @param parentTid
     */
    public List<ProviderTrade> findListByParentId(String parentTid) {
        return providerTradeRepository.findListByParentId(parentTid);
    }

    /**
     * 根据父订单列表查询供货商订单
     */
    public List<ProviderTrade> findListByParentIdList(List<String> parentTidList){
        return providerTradeRepository.findByParentIdIn(parentTidList);
    }

    /**
     *
     */
    public ProviderTrade findbyId(String id){
        return providerTradeRepository.findFirstById(id);
    }

    /**
     * 查询订单
     *
     * @param tid
     */
    public ProviderTrade providerDetail(String tid) {
        return providerTradeRepository.findById(tid).orElse(null);
    }

    /**
     * 更新订单
     *
     * @param tradeUpdateRequest
     */
    @GlobalTransactional
    @Transactional
    public void updateProviderTrade(TradeUpdateRequest tradeUpdateRequest) {
        this.updateProviderTrade(KsBeanUtil.convert(tradeUpdateRequest.getTrade(), ProviderTrade.class));
    }

    /**
     * 订单分页
     *
     * @param whereCriteria 条件
     * @param request       参数
     * @return
     */
    public Page<ProviderTrade> providerPage(Criteria whereCriteria, ProviderTradeQueryRequest request) {
        long totalSize = this.countNum(whereCriteria, request);
        if (totalSize < 1) {
            return new PageImpl<>(new ArrayList<>(), request.getPageRequest(), totalSize);
        }
        request.putSort(request.getSortColumn(), request.getSortRole());
        Query query = new Query(whereCriteria);
        return new PageImpl<>(mongoTemplate.find(query.with(request.getPageRequest()), ProviderTrade.class), request
                .getPageable(), totalSize);
    }

    /**
     * 统计数量
     *
     * @param whereCriteria
     * @param request
     * @return
     */
    public long countNum(Criteria whereCriteria, ProviderTradeQueryRequest request) {
        request.putSort(request.getSortColumn(), request.getSortRole());
        Query query = new Query(whereCriteria);
        long totalSize = mongoTemplate.count(query, ProviderTrade.class);
        return totalSize;
    }

    /**
     * 取消供应商订单
     *
     * @param parentId 父订单id
     * @param operator 操作人
     * @param isAuto   是否定时取消
     */
    @Transactional
    @GlobalTransactional
    public void providerCancel(String parentId, Operator operator, boolean isAuto) {
        List<ProviderTrade> providerTradeList = this.findListByParentId(parentId);

        if (CollectionUtils.isNotEmpty(providerTradeList)) {
            String msg = "用户取消订单";
            if (isAuto) {
                msg = "订单超时未支付，系统自动取消";
            }
            final String data = msg;

            providerTradeList.forEach(providerTrade -> {
                // 更新供应商订单状态为已作废
                providerTrade.getTradeState().setFlowState(FlowState.VOID);
                providerTrade.getTradeState().setEndTime(LocalDateTime.now());
                providerTrade.appendTradeEventLog(new TradeEventLog(operator, "取消订单", data, LocalDateTime.now()));
                this.updateProviderTrade(providerTrade);
            });
        }
    }

    /**
     * 审核供应商订单
     *
     * @param parentId
     * @param reason
     * @param auditState
     */
    public void providerAudit(String parentId, String reason, AuditState auditState) {
        List<ProviderTrade> providerTradeList = this.findListByParentId(parentId);

        if (CollectionUtils.isNotEmpty(providerTradeList)) {
            providerTradeList.forEach(providerTrade -> {
                // 更新供应商订单状态为已作废
                providerTrade.getTradeState().setAuditState(auditState);
                if (AuditState.REJECTED == auditState) {
                    providerTrade.getTradeState().setObsoleteReason(reason);
                } else {
                    providerTrade.getTradeState().setFlowState(FlowState.AUDIT);
                }
                this.updateProviderTrade(providerTrade);
            });
        }

    }

    /**
     * 发货校验,检查请求发货商品数量是否符合应发货数量
     *
     * @param tid                 订单id
     * @param tradeDeliverRequest 发货请求参数结构
     */
    public void deliveryCheck(String tid, TradeDeliverRequest tradeDeliverRequest) {
        ProviderTrade providerTrade = providerDetail(tid);
        Map<String, TradeItem> skusMap =
                providerTrade.getTradeItems().stream().collect(Collectors.toMap(TradeItem::getSkuId,
                Function.identity()));
        Map<String, TradeItem> giftsMap =
                providerTrade.getGifts().stream().collect(Collectors.toMap(TradeItem::getSkuId,
                Function.identity()));
        tradeDeliverRequest.getShippingItemList().forEach(i -> {
            TradeItem tradeItem = skusMap.get(i.getSkuId());
            if (tradeItem.getDeliveredNum() + i.getItemNum() > tradeItem.getNum()) {
                throw new SbcRuntimeException("K-050315");
            }
        });
        tradeDeliverRequest.getGiftItemList().forEach(i -> {
            TradeItem tradeItem = giftsMap.get(i.getSkuId());
            if (tradeItem.getDeliveredNum() + i.getItemNum() > tradeItem.getNum()) {
                throw new SbcRuntimeException("K-050315");
            }
        });
    }

    /**
     * 发货
     *
     * @param tid
     * @param tradeDeliver
     * @param operator
     * @return
     */
    public String deliver(String tid, TradeDeliver tradeDeliver, Operator operator) {
        ProviderTrade providerTrade = providerDetail(tid);
        //是否开启订单审核
        // todo 这里需要重新设置 李广
        SupplierOrderAuditRequest auditRequest = new SupplierOrderAuditRequest();
        auditRequest.setStoreIdAtSaaS(providerTrade.getSupplier().getStoreId());
        if (auditQueryProvider.isSupplierOrderAudit(auditRequest).getContext().isAudit() && providerTrade.getTradeState().getAuditState()
                != AuditState.CHECKED) {
            //只有已审核订单才能发货
            throw new SbcRuntimeException("K-050317");
        }
        // 先款后货并且未支付的情况下禁止发货
        if (providerTrade.getPaymentOrder() == PaymentOrder.PAY_FIRST && providerTrade.getTradeState().getPayState() == PayState.NOT_PAID && providerTrade.getPayInfo().getPayTypeId().equals("0")) {
            throw new SbcRuntimeException("K-050318");
        }
        if (verifyAfterProcessing(providerTrade.getParentId())) {
            throw new SbcRuntimeException("K-050114", new Object[]{providerTrade.getParentId()});
        }

        //规避发货重复
//        checkLogisticsNo(tradeDeliver.getLogistics().getLogisticNo(), tradeDeliver.getLogistics()
//                .getLogisticStandardCode());

        // 生成ID
        tradeDeliver.setDeliverId(generatorService.generate("TD"));
        tradeDeliver.setStatus(DeliverStatus.SHIPPED);
        tradeDeliver.setProviderName(providerTrade.getSupplier().getSupplierName());
        tradeDeliver.setTradeId(tid);

        List<TradeDeliver> tradeDelivers = providerTrade.getTradeDelivers();
        tradeDelivers.add(0,tradeDeliver);
        providerTrade.setTradeDelivers(tradeDelivers);

        providerTrade.getTradeItems().forEach(tradeItem -> {
            // 当前商品本次发货信息
            List<ShippingItem> shippingItems = tradeDeliver.getShippingItems().stream()
                    .filter(shippingItem -> tradeItem.getSkuId().equals(shippingItem.getSkuId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shippingItems)) {
                // 当前商品发货数量加上本次发货数量
                tradeItem.setDeliveredNum(shippingItems.get(0).getItemNum() + tradeItem.getDeliveredNum());
                // 判断当前商品是否已全部发货
                // TODO 待优化,目前发现有赞商品发货会存在多发货情况(实际并没有多发,只是计算发货数量有问题),这里统一拦截处理一下
                if (tradeItem.getNum() <= tradeItem.getDeliveredNum()) {
                    tradeItem.setDeliverStatus(DeliverStatus.SHIPPED);
                } else {
                    tradeItem.setDeliverStatus(DeliverStatus.PART_SHIPPED);
                }
            }
        });

        //赠品
        providerTrade.getGifts().forEach(gift -> {
            // 当前赠品本次发货信息
            List<ShippingItem> shippingItems = tradeDeliver.getGiftItemList().stream()
                    .filter(shippingItem -> gift.getSkuId().equals(shippingItem.getSkuId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shippingItems)) {
                // 当前赠品发货数量加上本次发货数量
                gift.setDeliveredNum(shippingItems.get(0).getItemNum() + gift.getDeliveredNum());
                // 判断赠品商品是否已全部发货
                if (gift.getNum().equals(gift.getDeliveredNum())) {
                    gift.setDeliverStatus(DeliverStatus.SHIPPED);
                } else {
                    gift.setDeliverStatus(DeliverStatus.PART_SHIPPED);
                }
            }
        });

        // 根据子单id查询该子单中所有已完成退款的SKU
        List<String> completedSkuIds = this.returnOrderService.getCompletedSkuIdsByPtid(tid);
        // 过滤掉已退款的商品
        List<TradeItem> tradeItems = providerTrade.getTradeItems();
        if(CollectionUtils.isNotEmpty(completedSkuIds)){
            tradeItems = tradeItems.stream().filter(i->!completedSkuIds.contains(i.getSkuId())).collect(Collectors.toList());
        }
        // 判断本次发货后，是否还有部分发货或未发货的商品，来设置订单发货状态
        Long partShippedNum = tradeItems.stream()
                .filter(tradeItem -> (tradeItem.getDeliverStatus() == DeliverStatus.PART_SHIPPED ||
                        tradeItem.getDeliverStatus() == DeliverStatus.NOT_YET_SHIPPED)).count();

        // 过滤掉已退款的赠品
        List<TradeItem> gifts = providerTrade.getGifts();
        if(CollectionUtils.isNotEmpty(completedSkuIds)){
            gifts = gifts.stream().filter(i->!completedSkuIds.contains(i.getSkuId())).collect(Collectors.toList());
        }
        //赠品
        Long giftsNum = gifts.stream().filter(gift -> DeliverStatus.NOT_YET_SHIPPED == gift.getDeliverStatus() ||
                DeliverStatus.PART_SHIPPED == gift.getDeliverStatus()).count();
        partShippedNum += giftsNum;

        //添加操作日志
        String detail = String.format("订单[%s]已%s,操作人：%s", providerTrade.getId(), "发货", operator.getName());
        if (partShippedNum.intValue() != 0) {
            providerTrade.getTradeState().setFlowState(FlowState.DELIVERED_PART);
            providerTrade.getTradeState().setDeliverStatus(DeliverStatus.PART_SHIPPED);
            detail = String.format("订单[%s]已%s,操作人：%s", providerTrade.getId(), "部分发货", operator.getName());
        } else {
            providerTrade.getTradeState().setFlowState(FlowState.DELIVERED);
            providerTrade.getTradeState().setDeliverStatus(DeliverStatus.SHIPPED);
        }

        providerTrade.appendTradeEventLog(TradeEventLog
                .builder()
                .operator(operator)
                .eventType(partShippedNum.intValue() != 0 ? FlowState.DELIVERED_PART.getDescription() : TradeEvent.DELIVER.getDescription())
                .eventTime(LocalDateTime.now())
                .eventDetail(detail)
                .build());

        // 更新发货信息
        this.updateProviderTrade(providerTrade);

        tradeDeliver.setSunDeliverId(tradeDeliver.getDeliverId());
        tradeDeliver.setShipperType(ShipperType.PROVIDER);
        tradeService.deliver(providerTrade.getParentId(), tradeDeliver, operator, BoolFlag.NO);

        return tradeDeliver.getDeliverId();
    }

    /**
     * 子单批量发货处理
     */
    public String dealBatchDeliver(ProviderTrade providerTrade, TradeDeliver tradeDeliver, Operator operator) {

//        if (verifyAfterProcessing(providerTrade.getParentId())) {
//            throw new SbcRuntimeException("K-050114", new Object[]{providerTrade.getId()});
//        }

        //子单发货清单tradeDeliver部分信息重置
        List<TradeItem> tradeItems = providerTrade.getTradeItems();
        List<ShippingItem> shippingItems = tradeItems.stream().map(item -> {
            //默认全部发货
            item.setDeliveredNum(item.getNum());
            item.setDeliverStatus(DeliverStatus.SHIPPED);
            ShippingItem shippingItem = KsBeanUtil.copyPropertiesThird(item, ShippingItem.class);
            shippingItem.setItemName(item.getSkuName());
            shippingItem.setItemNum(item.getNum());
            return shippingItem;
        }).collect(Collectors.toList());
        List<ShippingItem> giftItems = providerTrade.getGifts().stream().map(item -> {
            item.setDeliveredNum(item.getNum());
            item.setDeliverStatus(DeliverStatus.SHIPPED);
            ShippingItem shippingItem = KsBeanUtil.copyPropertiesThird(item, ShippingItem.class);
            shippingItem.setItemName(item.getSkuName());
            shippingItem.setItemNum(item.getNum());
            return shippingItem;
        }).collect(Collectors.toList());
        tradeDeliver.setShippingItems(shippingItems);
        tradeDeliver.setGiftItemList(giftItems);
        tradeDeliver.setTradeId(providerTrade.getId());
        providerTrade.addTradeDeliver(tradeDeliver);

        providerTrade.getTradeItems().forEach(tradeItem -> {
            tradeItem.setDeliveredNum(tradeItem.getNum());
            tradeItem.setDeliverStatus(DeliverStatus.SHIPPED);
        });
        //赠品
        providerTrade.getGifts().forEach(gift -> {
                gift.setDeliveredNum(gift.getNum());
                gift.setDeliverStatus(DeliverStatus.SHIPPED);
        });

        providerTrade.getTradeState().setFlowState(FlowState.DELIVERED);
        providerTrade.getTradeState().setDeliverStatus(DeliverStatus.SHIPPED);
        //添加操作日志
        String detail = String.format("订单[%s]已%s,操作人：%s", providerTrade.getId(), "全部发货", operator.getName());
        providerTrade.appendTradeEventLog(TradeEventLog
                .builder()
                .operator(operator)
                .eventType(FlowState.DELIVERED.getDescription())
                .eventTime(LocalDateTime.now())
                .eventDetail(detail)
                .build());

        // 更新发货信息
        this.updateProviderTrade(providerTrade);
        return tradeDeliver.getDeliverId();
    }

    /**
     * 验证订单是否存在售后申请
     *
     * @param tid
     * @return true|false:存在售后，阻塞订单进程|不存在售后，订单进程正常
     */
    public boolean verifyAfterProcessing(String tid) {
        List<ReturnOrder> returnOrders = returnOrderRepository.findByTid(tid);
        if (!CollectionUtils.isEmpty(returnOrders)) {
            // 查询是否存在正在进行中的退单(不是作废,不是拒绝退款,不是已结束)
            Optional<ReturnOrder> optional = returnOrders.stream().filter(item -> item.getReturnFlowState() !=
                    ReturnFlowState.VOID
                    && item.getReturnFlowState() != ReturnFlowState.REJECT_REFUND
                    && item.getReturnFlowState() != ReturnFlowState.COMPLETED).findFirst();
            if (optional.isPresent()) {
                return true;
            }

        }
        return false;
    }

    /**
     * 物流单号重复校验
     *
     * @param logisticsNo
     * @param logisticStandardCode
     */
    private void checkLogisticsNo(String logisticsNo, String logisticStandardCode) {
        if (providerTradeRepository
                .findTopByTradeDelivers_Logistics_LogisticNoAndTradeDelivers_Logistics_logisticStandardCode(logisticsNo,
                        logisticStandardCode)
                .isPresent()) {
            throw new SbcRuntimeException("K-050124");
        }
    }

    /**
     * 查询导出数据
     *
     * @param queryRequest
     */
    public List<ProviderTrade> listProviderTradeExport(ProviderTradeQueryRequest queryRequest) {
        long count = this.countNum(queryRequest.getWhereCriteria(), queryRequest);
        log.info("export count={}",count);
        if (count == 0) {
            return new ArrayList<>();
        }
        count = Math.min(count, Long.parseLong(exportTradeNum));
        queryRequest.putSort(queryRequest.getSortColumn(), queryRequest.getSortRole());
        queryRequest.setPageNum(0);
        queryRequest.setPageSize((int) count);

        //设置返回字段
        Map fieldsObject = new HashMap(32);
        fieldsObject.put("_id", Boolean.TRUE);
        fieldsObject.put("parentId", Boolean.TRUE);
        fieldsObject.put("tradeState.createTime", Boolean.TRUE);
        fieldsObject.put("supplierName", Boolean.TRUE);
        fieldsObject.put("supplierCode", Boolean.TRUE);
        fieldsObject.put("consignee.name", Boolean.TRUE);
        fieldsObject.put("consignee.phone", Boolean.TRUE);
        fieldsObject.put("consignee.detailAddress", Boolean.TRUE);
        fieldsObject.put("deliverWay", Boolean.TRUE);
        fieldsObject.put("tradePrice.goodsPrice", Boolean.TRUE);

        fieldsObject.put("tradePrice.totalPrice", Boolean.TRUE);
        fieldsObject.put("tradeItems.skuId", Boolean.TRUE);
        fieldsObject.put("tradeItems.skuNo", Boolean.TRUE);
        fieldsObject.put("tradeItems.providerSkuNo", Boolean.TRUE);
        fieldsObject.put("tradeItems.deliveredNum", Boolean.TRUE);
        fieldsObject.put("tradeItems.specDetails", Boolean.TRUE);
        fieldsObject.put("tradeItems.skuName", Boolean.TRUE);
        fieldsObject.put("tradeItems.num", Boolean.TRUE);
        fieldsObject.put("tradeItems.cateId", Boolean.TRUE);
        fieldsObject.put("tradeItems.supplyPrice", Boolean.TRUE);
        fieldsObject.put("tradeItems.totalSupplyPrice", Boolean.TRUE);

        fieldsObject.put("buyerRemark", Boolean.TRUE);
        fieldsObject.put("sellerRemark", Boolean.TRUE);
        fieldsObject.put("tradeState.flowState", Boolean.TRUE);
        fieldsObject.put("tradeState.payState", Boolean.TRUE);
        fieldsObject.put("tradeState.deliverStatus", Boolean.TRUE);
        fieldsObject.put("grouponFlag", Boolean.TRUE);
        fieldsObject.put("tradeGroupon.grouponOrderStatus", Boolean.TRUE);
//        fieldsObject.put("invoice.type", true);
//        fieldsObject.put("invoice.projectName", true);
//        fieldsObject.put("invoice.generalInvoice.title", true);
//        fieldsObject.put("invoice.specialInvoice.companyName", true);
//        fieldsObject.put("supplier.supplierName", true);

        // 设置订单状态为已支付
        TradeConfigGetByTypeRequest request = new TradeConfigGetByTypeRequest();
        request.setConfigType(ConfigType.ORDER_SETTING_PAYMENT_ORDER);
        request.setStoreId(Constants.BOSS_DEFAULT_STORE_ID);
        Integer paymentOrder = tradeCacheService.getTradeConfigByType(request).getStatus();
        if(PaymentOrder.PAY_FIRST == PaymentOrder.values()[paymentOrder]) {
            queryRequest.getTradeState().setPayState(PayState.PAID);
        }
        Query query = new BasicQuery(new Document(), new Document(fieldsObject));
        query.addCriteria(queryRequest.getWhereCriteria());
        List<ProviderTrade> tradeList = mongoTemplate.find(query.with(queryRequest.getPageRequest()), ProviderTrade.class);

        if(null != tradeList){
            log.info("export size={}",tradeList.size());
        }
        return tradeList;
    }


    /**
     * 查询订单集合
     *
     * @param tids
     */
    public List<ProviderTrade> details(List<String> tids) {
        return org.apache.commons.collections4.IteratorUtils.toList(providerTradeRepository.findAllById(tids).iterator());
    }

    /**
     * 查询全部订单
     *
     * @param request
     * @return
     */
    public List<ProviderTrade> queryAll(ProviderTradeQueryRequest request) {
        return mongoTemplate.find(new Query(request.getWhereCriteria()), ProviderTrade.class);
    }


    /**
     * 修改备注
     *
     * @param tid
     * @param buyerRemark
     */
    @Transactional
    public void remedyBuyerRemark(String tid, String buyerRemark, Operator operator) {
        //1、查找订单信息
        ProviderTrade providerTrade = providerDetail(tid);
        providerTrade.setBuyerRemark(buyerRemark);
        Trade trade = tradeService.detail(providerTrade.getParentId());
        providerTrade.appendTradeEventLog(new TradeEventLog(operator, "修改备注", "修改供应商订单备注", LocalDateTime.now()));
        trade.appendTradeEventLog(new TradeEventLog(operator, "修改备注", "修改供应商订单备注", LocalDateTime.now()));
        //保存
        providerTradeRepository.save(providerTrade);
        tradeService.updateTrade(trade);
        this.operationLogMq.convertAndSend(operator, "修改供应商订单备注", "修改供应商订单备注");
    }

    public String providerByidAndPid(String tid, String providerId) {
        String providerTradeId = StringUtils.EMPTY;
        List<ProviderTrade> providerTrades = this.findListByParentId(tid);
        for(ProviderTrade providerTrade:providerTrades){
            if(Long.parseLong(providerId) == providerTrade.getSupplier().getStoreId()){
                providerTradeId = providerTrade.getId();
            }
        }
        return providerTradeId;
    }

    /**
     * 根据主订单id，供应商(或商家)id获取子订单
     * @param tid
     * @param providerId
     * @return
     */
    public List<ProviderTrade> getProviderTradeByIdAndPid(String tid, Long providerId) {
        List<ProviderTrade> providerTrades = this.findListByParentId(tid);
        // 2022年4月2日14:48:50, 混组商品, 可能存在一个供应商对应 一组虚拟订单和一组实物订单
        List<ProviderTrade> providerTradeList = providerTrades.stream()
                .filter(trade -> providerId.longValue() == trade.getSupplier().getStoreId().longValue())
                .collect(Collectors.toList());
        return providerTradeList;
    }

    /**
     * 发货记录作废
     *
     * @param tid
     * @param deliverId
     * @param operator
     */
    @Transactional
    public void deliverRecordObsolete(String tid, String deliverId, Operator operator) {

        ProviderTrade providerTrade = providerTradeRepository.findFirstById(tid);
        List<TradeDeliver> tradeDelivers = providerTrade.getTradeDelivers();
        //查询发货记录
        Optional<TradeDeliver> tradeDeliverOptional = tradeDelivers
                .stream()
                .filter(tradeDeliver -> StringUtils.equals(deliverId, tradeDeliver.getDeliverId()))
                .findFirst();

        if (tradeDeliverOptional.isPresent()) {
            StringBuilder stringBuilder = new StringBuilder(200);

            TradeDeliver tradeDeliver = tradeDeliverOptional.get();

            //处理商品
            handleShippingItems(providerTrade, tradeDeliver.getShippingItems(), stringBuilder, false);

            //订单状态更新
            TradeState tradeState = providerTrade.getTradeState();
            if (isAllNotShipped(providerTrade)) {
                tradeState.setFlowState(FlowState.AUDIT);
                tradeState.setDeliverStatus(DeliverStatus.NOT_YET_SHIPPED);
            } else {
                tradeState.setFlowState(FlowState.DELIVERED_PART);
                tradeState.setDeliverStatus(DeliverStatus.PART_SHIPPED);
            }

            //添加操作日志
            stringBuilder.trimToSize();
            providerTrade.appendTradeEventLog(TradeEventLog
                    .builder()
                    .operator(operator)
                    .eventType(TradeEvent.OBSOLETE_DELIVER.getDescription())
                    .eventDetail(stringBuilder.toString())
                    .eventTime(LocalDateTime.now())
                    .build());

            //删除发货单
            tradeDelivers.remove(tradeDeliver);

            //保存
            providerTradeRepository.save(providerTrade);
            operationLogMq.convertAndSend(operator, TradeEvent.OBSOLETE_DELIVER.getDescription(), stringBuilder.toString());

        }
    }

    @Transactional
    public void handleShippingItems(ProviderTrade trade, List<ShippingItem> shippingItems, StringBuilder stringBuilder, boolean isGift) {
        ConcurrentHashMap<String, TradeItem> skuItemMap ;
        if(isGift){
            skuItemMap = trade.giftSkuItemMap();
        }else{
            skuItemMap = trade.skuItemMap();
        }

        //订单商品更新
        shippingItems.forEach(shippingItem -> {
            TradeItem tradeItem = skuItemMap.get(shippingItem.getSkuId());

            Long shippedNum = tradeItem.getDeliveredNum();
            shippedNum -= shippingItem.getItemNum();
            tradeItem.setDeliveredNum(shippedNum);

            if (shippedNum.equals(0L)) {
                tradeItem.setDeliverStatus(DeliverStatus.NOT_YET_SHIPPED);
            } else if (shippedNum < tradeItem.getNum()) {
                tradeItem.setDeliverStatus(DeliverStatus.PART_SHIPPED);
            }

            stringBuilder.append(String.format("订单[%s],商品[%s], 作废发货[%s], 目前状态:[%s]\r\n",
                    trade.getId(),
                    (isGift ? "【赠品】" : "" ) + tradeItem.getSkuName(),
                    shippingItem.getItemNum().toString(),
                    tradeItem.getDeliverStatus().getDescription())
            );

        });
    }

    /**
     * 是否全部未发货
     *
     * @param trade
     * @return
     */
    @Transactional
    public boolean isAllNotShipped(ProviderTrade trade) {
        List<TradeItem> allItems = new ArrayList<>();
        allItems.addAll(trade.getTradeItems());
        allItems.addAll(trade.getGifts());
        List<TradeItem> collect = allItems.stream()
                .filter(tradeItem -> !tradeItem.getDeliveredNum().equals(0L))
                .collect(Collectors.toList());
        return collect.isEmpty();
    }


    public List<ProviderTrade> findTradeListForSettlement(Long storeId, Date startTime, Date endTime, PageRequest pageRequest) {
        Criteria criteria = new Criteria();
        criteria.andOperator(Criteria.where("supplier.storeId").is(storeId)
                , new Criteria().orOperator(
                        Criteria.where("tradeState.flowState").is(FlowState.COMPLETED),
                        Criteria.where("tradeState.flowState").is(FlowState.VOID),
                        Criteria.where("refundFlag").is(Boolean.TRUE))
                , Criteria.where("tradeState.deliverStatus").in(Arrays.asList(DeliverStatus.SHIPPED,
                        DeliverStatus.PART_SHIPPED))
                , Criteria.where("returnOrderNum").is(0)
                , Criteria.where("tradeState.finalTime").lt(endTime).gte(startTime)
        );

        return mongoTemplate.find(
                new Query(criteria).skip(pageRequest.getPageNumber() * pageRequest.getPageSize() * 1L).limit(pageRequest
                        .getPageSize())
                , ProviderTrade.class);
    }

    /**
     * 更新订单的待确认标识
     * @param providerTradeId 供应商订单号
     * @param payState 付款标识
     */
    @Transactional
    public void updateThirdPlatformPayState(String providerTradeId, PayState payState) {
        mongoTemplate.updateMulti(new Query(Criteria.where("id").is(providerTradeId)), new Update().set("tradeState.payState", payState), ProviderTrade.class);
    }

    /**
     * 更新订单的错误标识
     * @param providerTradeId 供应商订单号
     * @param errorFlag 错误标识
     */
    @Transactional
    public void updateThirdPlatformPayFlag(String providerTradeId, Boolean errorFlag) {
        mongoTemplate.updateMulti(new Query(Criteria.where("id").is(providerTradeId)), new Update().set("thirdPlatformPayErrorFlag", errorFlag), ProviderTrade.class);
    }

    /**
     * 更新正在进行的供应商订单数量
     * @param returnOrderId 退单id
     * @param addFlag 退单数加减状态
     */
    @Transactional
    public void updateReturnOrderNumByRid(String returnOrderId, boolean addFlag) {
        ReturnOrder order = returnOrderRepository.findById(returnOrderId).orElse(null);
        if (Objects.isNull(order)) {
            log.error("退单ID:{},查询不到退单信息", returnOrderId);
            return;
        }

        if (StringUtils.isNotBlank(order.getPtid())) {
            ProviderTrade trade = this.findbyId(order.getPtid());
            if (Objects.nonNull(trade)) {
                // 1.根据addFlag加减正在进行的退单
                Integer num = trade.getReturnOrderNum() == null ? Integer.valueOf("0") : trade.getReturnOrderNum();
                mongoTemplate.updateFirst(new Query(Criteria.where("id").is(trade.getId())), new Update()
                        .set("returnOrderNum", addFlag ? ++num : --num), ProviderTrade.class);
            }
        }
    }

    /**
     * 根据条件查询父订单id
     * @param queryRequest
     * @return
     */
    public List<String> findParentIdByCondition(ProviderTradeQueryRequest queryRequest){
        //设置返回字段
        Map fieldsObject = new HashMap();
        fieldsObject.put("parentId", Boolean.TRUE);
        Query query = new BasicQuery(new Document(), new Document(fieldsObject));
        query.addCriteria(queryRequest.getWhereCriteria());
        List<ProviderTrade> providerTrades = mongoTemplate.find(query, ProviderTrade.class);
        List<String> parentIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(providerTrades)){
            List<String> ids = providerTrades.stream().map(ProviderTrade::getParentId).collect(Collectors.toList());
            parentIds.addAll(ids);
        }
        return parentIds;
    }
}
