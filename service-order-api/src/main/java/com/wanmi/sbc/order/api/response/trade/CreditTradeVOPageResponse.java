package com.wanmi.sbc.order.api.response.trade;

import com.wanmi.sbc.common.base.MicroServicePage;
import com.wanmi.sbc.order.bean.vo.TradeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/4/7 15:45
 * @description <p> 订单信息响应体 </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel
public class CreditTradeVOPageResponse implements Serializable {

    private static final long serialVersionUID = 7365738171364510736L;

    @ApiModelProperty(value = "查询订单返回结果")
    private MicroServicePage<TradeVO> tradeVOList;
}