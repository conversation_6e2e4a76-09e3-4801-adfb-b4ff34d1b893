package com.wanmi.sbc.order.api.response.settlement;

import com.wanmi.sbc.order.bean.vo.TradeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 查询结算明细对应的订单Map的Response
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettlementDetailTradeListResponse implements Serializable{
    private static final long serialVersionUID = 1L;

    /**
     * 结算明细对应的订单Map {@link TradeVO}
     */
    @ApiModelProperty(value = "结算明细对应的订单Map")
    private Map<String, TradeVO> tradeVOMap;
}
