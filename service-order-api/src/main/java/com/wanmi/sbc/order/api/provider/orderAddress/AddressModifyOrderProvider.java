package com.wanmi.sbc.order.api.provider.orderAddress;

import com.wanmi.sbc.common.base.BaseResponse;
import com.wanmi.sbc.order.api.request.orderAddress.AddressModifyOrderAuditRequest;
import com.wanmi.sbc.order.api.request.orderAddress.AddressModifyOrderByOrderIdAndTidRequest;
import com.wanmi.sbc.order.api.request.orderAddress.AddressModifyOrderByTidRequest;
import com.wanmi.sbc.order.api.request.orderAddress.AddressModifyOrderAddRequest;
import com.wanmi.sbc.order.api.response.orderAddress.AddressModifyOrderResponse;
import com.wanmi.sbc.order.api.response.orderAddress.AddressModifyRefuseReasonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 订单地址变更服务
 */
@FeignClient(value = "${application.order.name}", contextId = "AddressModifyOrderProvider")
public interface AddressModifyOrderProvider {

    /**
     * 查询当前订单最新变更地址申请工单
     * @param addressModifyOrderByTidRequest {@link AddressModifyOrderByTidRequest }
     * @return {@link AddressModifyOrderResponse }
     */
    @PostMapping("/order/${application.order.version}/address/getNewestOrder")
    BaseResponse<AddressModifyOrderResponse> getNewestOrder(@RequestBody @Valid AddressModifyOrderByTidRequest addressModifyOrderByTidRequest);

    /**
     * 保存变更地址申请工单和不同店铺审核记录，更新订单当前是否有审核中的变更地址申请和操作日志（订单和供应商/福利主子单）
     * @param addressModifyOrderAddRequest {@link AddressModifyOrderAddRequest }
     * @return {@link AddressModifyOrderResponse }
     */
    @PostMapping("/order/${application.order.version}/address/add")
    BaseResponse addOrderAndSyncTrade(@RequestBody @Valid AddressModifyOrderAddRequest addressModifyOrderAddRequest);

    /**
     * 查询所有审核拒绝原因枚举项
     * @return 审核拒绝原因枚举项列表 {@link AddressModifyRefuseReasonResponse }
     */
    @PostMapping("/order/${application.order.version}/address/get-all-refuse-reason")
    BaseResponse<AddressModifyRefuseReasonResponse> getAllRefuseReason();

    /**
     * 查询工单和相应审核记录
     * @param addressModifyOrderByOrderIdAndTidRequest {@link AddressModifyOrderByOrderIdAndTidRequest}
     * @return 工单信息 {@link AddressModifyOrderResponse }
     */
    @PostMapping("/order/${application.order.version}/address/get-order-with-audit-record")
    BaseResponse<AddressModifyOrderResponse> getOrderWithAuditRecord(
            @RequestBody @Valid AddressModifyOrderByOrderIdAndTidRequest addressModifyOrderByOrderIdAndTidRequest);

    /**
     * 审核通过/拒绝处理工单和审核记录，同步订单处理相应数据
     * @param addressModifyOrderAuditRequest {@link AddressModifyOrderAuditRequest}
     * @return {@link BaseResponse }
     */
    @PostMapping("/order/${application.order.version}/address/audit-pass-or-refuse-and-sync-trade")
    BaseResponse auditPassOrRefuseAndSyncTrade(@RequestBody @Valid AddressModifyOrderAuditRequest addressModifyOrderAuditRequest);
}