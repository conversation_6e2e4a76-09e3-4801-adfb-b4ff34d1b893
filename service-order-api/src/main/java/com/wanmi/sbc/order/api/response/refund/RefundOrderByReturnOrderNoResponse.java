package com.wanmi.sbc.order.api.response.refund;

import com.wanmi.sbc.order.bean.vo.RefundOrderVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 退款单返回
 * Created by <PERSON>hangjin on 2017/4/30.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class RefundOrderByReturnOrderNoResponse extends RefundOrderVO implements Serializable{


    private static final long serialVersionUID = 1L;
}
