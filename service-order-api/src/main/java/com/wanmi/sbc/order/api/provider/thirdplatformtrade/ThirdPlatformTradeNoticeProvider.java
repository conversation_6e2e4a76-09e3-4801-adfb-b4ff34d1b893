package com.wanmi.sbc.order.api.provider.thirdplatformtrade;

import com.wanmi.sbc.common.base.BaseResponse;
import com.wanmi.sbc.order.api.request.thirdplatformtrade.ThirdPlatformTradeNoticeRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 订单状态通知
 *
 * <AUTHOR>
 * @desc
 * @date 2022-04-02
 */
@FeignClient(value = "${application.order.name}", contextId = "ThirdPlatformTradeNoticeProvider")
public interface ThirdPlatformTradeNoticeProvider {



    @PostMapping("/order/${application.order.version}/third-platform-trade/trade-notice")
    BaseResponse tradeNotice(@RequestBody ThirdPlatformTradeNoticeRequest request);
}
