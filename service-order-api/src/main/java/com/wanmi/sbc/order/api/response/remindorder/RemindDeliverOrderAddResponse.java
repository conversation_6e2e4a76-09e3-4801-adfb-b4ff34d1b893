package com.wanmi.sbc.order.api.response.remindorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemindDeliverOrderAddResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 提醒发货次数
     */
    @ApiModelProperty(value = "remindCount")
    private Integer remindCount;
}