package com.wanmi.sbc.order.api.response.refundcallbackresult;

import com.wanmi.sbc.common.base.MicroServicePage;
import com.wanmi.sbc.order.bean.vo.RefundCallBackResultVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>退款回调结果分页结果</p>
 * <AUTHOR>
 * @date 2020-07-01 17:34:23
 */
@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundCallBackResultPageResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 退款回调结果分页结果
     */
    @ApiModelProperty(value = "支付回调结果分页结果")
    private MicroServicePage<RefundCallBackResultVO> page;
}
