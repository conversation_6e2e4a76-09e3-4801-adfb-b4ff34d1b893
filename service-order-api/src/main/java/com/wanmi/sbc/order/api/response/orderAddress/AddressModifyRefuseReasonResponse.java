package com.wanmi.sbc.order.api.response.orderAddress;

import com.wanmi.sbc.order.bean.vo.AddressModifyRefuseReasonVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 变更地址审核拒绝原因列表Response
 */
@Data
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddressModifyRefuseReasonResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "变更地址审核拒绝原因列表")
    private List<AddressModifyRefuseReasonVO> refuseReasonList;
}