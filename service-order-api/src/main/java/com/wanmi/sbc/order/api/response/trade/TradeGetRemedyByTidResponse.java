package com.wanmi.sbc.order.api.response.trade;

import com.wanmi.sbc.order.bean.vo.TradeRemedyDetailsVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: Zhang<PERSON>ing<PERSON>e
 * @Description:
 * @Date: 2018-12-05 15:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class TradeGetRemedyByTidResponse implements Serializable {

    /**
     * 废弃单
     */
    @ApiModelProperty(value = "废弃单")
    private TradeRemedyDetailsVO tradeRemedyDetailsVO;

}
