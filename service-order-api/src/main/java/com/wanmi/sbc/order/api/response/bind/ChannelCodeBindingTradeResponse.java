package com.wanmi.sbc.order.api.response.bind;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wanmi.sbc.common.util.CustomLocalDateTimeDeserializer;
import com.wanmi.sbc.common.util.CustomLocalDateTimeSerializer;
import lombok.*;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ChannelCodeBindingTradeResponse {

    private String id;

    /**
     * 订单id
     */
    private String tid;

    /**
     * parentId
     */
    private String parentId;

    /**
     * 渠道码
     */
    private String channelCode;

    /**
     * 三方渠道码
     */
    private String externalCode;

    /**
     * 创建时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime createTime;
}
