package com.wanmi.sbc.order.api.response.trade;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wanmi.sbc.common.util.CustomLocalDateTimeDeserializer;
import com.wanmi.sbc.common.util.CustomLocalDateTimeSerializer;
import com.wanmi.sbc.order.bean.enums.FlowState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/1 15:13
 * @description <p> 授信订单信息响应体 </p>
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class CreditTradePageResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    private String customerAccount;

    /**
     * 订单应还款金额
     */
    @ApiModelProperty(value = "订单应还款金额")
    private BigDecimal orderPrice;

    /**
     * 订单原始金额
     */
    @ApiModelProperty(value = "订单原始金额")
    private BigDecimal orderOriginPrice;

    /**
     * 支付状态
     */
    @ApiModelProperty(value = "支付状态 0未支付 1待确认 2已支付")
    private String payOrderStatus;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private FlowState flowState;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime payTime;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址")
    private List<String> urlList;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private Integer goodsNum;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long storeId;

}