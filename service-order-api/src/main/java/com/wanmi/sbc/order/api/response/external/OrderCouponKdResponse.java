package com.wanmi.sbc.order.api.response.external;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderCouponKdResponse {
    private Integer id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 商品编号
     */
    private String sku;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 发送状态：0-未发送，1-成功，2-失败
     */
    private Integer sendStatus;

    /**
     * 发送次数
     */
    private Integer sendNum;

    /**
     * 发送
     */
    private String extendReq;

    /**
     * 响应结果
     */
    private String extendResp;

    private Date createTime;

    private Date updateTime;
}
