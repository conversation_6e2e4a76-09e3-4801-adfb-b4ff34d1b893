package com.wanmi.sbc.order.api.provider.remindorder;

import com.wanmi.sbc.common.base.BaseResponse;
import com.wanmi.sbc.order.api.response.remindorder.RemindDeliverOrderAddResponse;
import com.wanmi.sbc.order.bean.dto.RemindDeliverOrderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 催退货订单记录服务Provider
 *
 * <AUTHOR>
 */
@FeignClient(value = "${application.order.name}", contextId = "RemindDeliverOrderProvider")
public interface RemindDeliverOrderProvider {

    /**
     * 新增催货订单记录
     *
     * @param
     * @return
     */
    @PostMapping("/order/${application.order.version}/remind/deliver/add")
    BaseResponse<RemindDeliverOrderAddResponse> addRemindCount(@RequestBody @Valid RemindDeliverOrderDTO remindDeliverOrderDTO);

}

