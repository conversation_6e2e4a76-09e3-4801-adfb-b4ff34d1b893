package com.wanmi.sbc.order.api.response.trade;

import com.wanmi.sbc.order.bean.vo.TradeItemSnapshotVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 获取客户id查询已确认订单商品快照响应结构
 * @Author: daiyitian
 * @Description:
 * @Date: 2018-11-16 16:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class TradeItemSnapshotByCustomerIdResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快照对象
     */
    @ApiModelProperty(value = "快照对象")
    private TradeItemSnapshotVO tradeItemSnapshotVO;
}
