package com.wanmi.sbc.order.api.response.refundcallbackresult;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>退款回调结果新增参数</p>
 * <AUTHOR>
 * @date 2020-07-01 17:34:23
 */
@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundCallBackResultAddResponse implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 新增成功返回主键ID
	 */
	@ApiModelProperty(value = "ID")
	private String id;
}