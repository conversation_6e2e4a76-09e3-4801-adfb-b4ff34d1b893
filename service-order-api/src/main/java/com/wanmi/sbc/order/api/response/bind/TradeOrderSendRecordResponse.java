package com.wanmi.sbc.order.api.response.bind;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName TradeOrderSendRecordResponse
 * @Description TradeOrderSendRecordResponse
 * <AUTHOR>
 * @Date 2022/3/31 15:36
 * @Version 1.0
 */
@Data
public class TradeOrderSendRecordResponse {

    private Integer id;
    /**
     * 订单tid
     */
    private String tid;
    /**
     * 请求内容
     */
    private String extendRequest;
    /**
     * 响应内容
     */
    private String extendResponse;
    /**
     * 订单状态：1-发送成功，2-发送失败
     */
    private Boolean sendStatus;

    private Date createTime;

    private Date updateTime;
    /**
     * 扩展内容
     */
    private String extend;
}
