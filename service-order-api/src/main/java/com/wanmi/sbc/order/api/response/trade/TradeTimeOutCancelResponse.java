package com.wanmi.sbc.order.api.response.trade;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName TradeTimeOutCancelResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/9 21:50
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class TradeTimeOutCancelResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<String> sucTidList;

    private List<String> failTidList;
}
