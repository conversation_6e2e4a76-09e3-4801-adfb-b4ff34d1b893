package com.wanmi.sbc.order.api.response.trade;

import com.wanmi.sbc.order.bean.vo.ProviderTradeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: ZhangLingKe
 * @Description:
 * @Date: 2018-12-05 15:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class ProviderTradeListExportResponse implements Serializable {

    /**
     * 交易单列表
     */
    @ApiModelProperty(value = "交易单列表")
    private List<ProviderTradeVO> providerTradeVOList;

}
