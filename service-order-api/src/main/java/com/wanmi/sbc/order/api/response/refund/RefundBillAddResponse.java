package com.wanmi.sbc.order.api.response.refund;

import com.wanmi.sbc.order.bean.vo.RefundBillVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 退款单
 * Created by <PERSON><PERSON><PERSON> on 2017/4/21.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class RefundBillAddResponse extends RefundBillVO implements Serializable {

    private static final long serialVersionUID = 1L;
}
