package com.wanmi.sbc.order.api.response.refund;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class RefundOrderQueryPageForEsResponse implements Serializable {

    @ApiModelProperty(value = "退单返回")
    private List<RefundOrderVoFromEsResponse> refundOrderResponse;
}