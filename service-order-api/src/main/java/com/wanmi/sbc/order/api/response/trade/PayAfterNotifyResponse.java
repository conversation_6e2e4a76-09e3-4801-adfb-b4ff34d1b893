package com.wanmi.sbc.order.api.response.trade;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestMethod;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayAfterNotifyResponse {
    /**
     * 用来标志是否成功 true成功 false 失败
     */
    private Boolean success = Boolean.TRUE;

    private Long id;

    private String requestUrl;//请求地址   包含http:// or https://

    private String param;//参数json   如果有些需要设置headers 直接将全部json后存入

    private RequestMethod requestMethod;//请求类型 get、post、put、delete等

    private String bodyJsonString; //返回字符串 可以直观看到第一次失败原因

    private Integer retryStatus;//重试状态 重试成功后标志位改为true   不成功的 ++ 代表重试次数

    private LocalDateTime createTime;//创建时间 可以理解为第一次调用失败时间

    private String businessSource;//业务来源 简单标注 建议使用 : 来分割业务标志   参照redis使用 后期也可以按照该字段进行分组重试

    private String ext;//拓展字段

    private Integer delFlag;//删除标志位

}
