package com.wanmi.sbc.order.api.provider.settlement;

import com.wanmi.sbc.order.api.request.settlement.*;
import com.wanmi.sbc.common.base.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <p>对账结算明细操作接口</p>
 * Created by of628-we<PERSON><PERSON> on 2018-10-13-下午6:23.
 */
@FeignClient(value = "${application.order.name}", contextId = "SettlementDetailProvider")
public interface SettlementDetailProvider {

    /**
     * 新增计算明细列表
     *
     * @param settlementDetailListAddRequest 新增结算明细列表数据结构 {@link SettlementDetailListAddRequest}
     * @return {@link BaseResponse}
     */
    @PostMapping("/account/${application.order.version}/finance/record/settlement/detail/add-list")
    BaseResponse addList(@RequestBody @Valid SettlementDetailListAddRequest settlementDetailListAddRequest);

    /**
     * 新增单条结算明细
     *
     * @param settlementDetailAddRequest 新增结算明细数据结构 {@link SettlementDetailAddRequest}
     * @return {@link BaseResponse}
     */
    @PostMapping("/account/${application.order.version}/finance/record/settlement/detail/add")
    BaseResponse add(@RequestBody @Valid SettlementDetailAddRequest settlementDetailAddRequest);

    /**
     * 修改
     *
     * @return {@link BaseResponse}
     */
    @PostMapping("/account/${application.order.version}/finance/record/settlement/detail/update")
    BaseResponse update(@RequestBody @Valid SettlementDetailUpdateRequest settlementDetailUpdateRequest);

    /**
     * 根据条件删除结算明细
     *
     * @param settlementDetailDeleteRequest 删除条件 {@link SettlementDetailDeleteRequest}
     * @return {@link BaseResponse}
     */
    @PostMapping("/account/${application.order.version}/finance/record/settlement/detail/delete")
    BaseResponse delete(@RequestBody @Valid SettlementDetailDeleteRequest settlementDetailDeleteRequest);

    /**
     * 校验结算明细的付款方式是否全部为【独立收款】
     * @param settlementDetailCheckRequest 结算明细列表 {@link SettlementDetailCheckRequest}
     * @return 校验结果
     */
    @PostMapping("/account/${application.order.version}/finance/record/settlement/detail/checkAllIndependence")
    BaseResponse<Boolean> checkAllIndependence(@RequestBody @Valid SettlementDetailCheckRequest settlementDetailCheckRequest);

}
