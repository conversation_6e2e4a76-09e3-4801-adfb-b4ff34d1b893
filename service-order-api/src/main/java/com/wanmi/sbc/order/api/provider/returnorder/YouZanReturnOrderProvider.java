package com.wanmi.sbc.order.api.provider.returnorder;

import com.wanmi.sbc.common.base.BaseResponse;
import com.wanmi.sbc.order.api.request.linkedmall.LinkedMallReturnOrderApplyRequest;
import com.wanmi.sbc.order.api.response.returnorder.ReturnReasonListResponse;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>linkedMall退单服务接口</p>
 * @Author: da<PERSON>yitian
 * @Description: 退单服务接口
 * @Date: 2018-12-03 15:40
 */
@FeignClient(value = "${application.order.name}", contextId = "SbcYouZanReturnOrderProvider")
public interface YouZanReturnOrderProvider {


    /**
     * 同步退单状态
     *
     * @return 操作结果 {@link BaseResponse}
     */
    @PostMapping("/order/${application.order.version}/you-zan/sync-status")
    BaseResponse syncStatus();
}
